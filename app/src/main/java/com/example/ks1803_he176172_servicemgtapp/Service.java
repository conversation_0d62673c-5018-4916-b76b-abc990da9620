package com.example.ks1803_he176172_servicemgtapp;

public class Service {
    private int serviceNo;
    private String serviceName;
    private String type;
    private double price;

    // Constructor
    public Service() {
    }

    public Service(int serviceNo, String serviceName, String type, double price) {
        this.serviceNo = serviceNo;
        this.serviceName = serviceName;
        this.type = type;
        this.price = price;
    }

    public Service(String serviceName, String type, double price) {
        this.serviceName = serviceName;
        this.type = type;
        this.price = price;
    }

    // Getters and Setters
    public int getServiceNo() {
        return serviceNo;
    }

    public void setServiceNo(int serviceNo) {
        this.serviceNo = serviceNo;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    @Override
    public String toString() {
        return "Service{" +
                "serviceNo=" + serviceNo +
                ", serviceName='" + serviceName + '\'' +
                ", type='" + type + '\'' +
                ", price=" + price +
                '}';
    }
}
