package com.example.ks1803_he176172_servicemgtapp;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;

import java.util.ArrayList;
import java.util.List;

public class DatabaseHelper extends SQLiteOpenHelper {
    private static final String DATABASE_NAME = "ServiceDatabase";
    private static final int DATABASE_VERSION = 1;
    
    // Table name
    private static final String TABLE_SERVICES = "services";
    
    // Column names
    private static final String COLUMN_SERVICE_NO = "service_no";
    private static final String COLUMN_SERVICE_NAME = "service_name";
    private static final String COLUMN_TYPE = "type";
    private static final String COLUMN_PRICE = "price";
    
    // Create table SQL query
    private static final String CREATE_SERVICES_TABLE = "CREATE TABLE " + TABLE_SERVICES + "("
            + COLUMN_SERVICE_NO + " INTEGER PRIMARY KEY AUTOINCREMENT,"
            + COLUMN_SERVICE_NAME + " TEXT NOT NULL,"
            + COLUMN_TYPE + " TEXT NOT NULL,"
            + COLUMN_PRICE + " REAL NOT NULL" + ")";

    public DatabaseHelper(Context context) {
        super(context, DATABASE_NAME, null, DATABASE_VERSION);
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        db.execSQL(CREATE_SERVICES_TABLE);
        
        // Insert sample data
        insertSampleData(db);
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_SERVICES);
        onCreate(db);
    }

    private void insertSampleData(SQLiteDatabase db) {
        ContentValues values = new ContentValues();
        
        // Sample service 1
        values.put(COLUMN_SERVICE_NAME, "House clean");
        values.put(COLUMN_TYPE, "Housing");
        values.put(COLUMN_PRICE, 123);
        db.insert(TABLE_SERVICES, null, values);
        
        // Sample service 2
        values.clear();
        values.put(COLUMN_SERVICE_NAME, "Skin care");
        values.put(COLUMN_TYPE, "Health care");
        values.put(COLUMN_PRICE, 300);
        db.insert(TABLE_SERVICES, null, values);
        
        // Sample service 3
        values.clear();
        values.put(COLUMN_SERVICE_NAME, "Baby wash");
        values.put(COLUMN_TYPE, "Baby care");
        values.put(COLUMN_PRICE, 400);
        db.insert(TABLE_SERVICES, null, values);
    }

    // Add a new service
    public long addService(Service service) {
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();
        
        values.put(COLUMN_SERVICE_NAME, service.getServiceName());
        values.put(COLUMN_TYPE, service.getType());
        values.put(COLUMN_PRICE, service.getPrice());
        
        long id = db.insert(TABLE_SERVICES, null, values);
        db.close();
        return id;
    }

    // Get all services
    public List<Service> getAllServices() {
        List<Service> serviceList = new ArrayList<>();
        String selectQuery = "SELECT * FROM " + TABLE_SERVICES;
        
        SQLiteDatabase db = this.getWritableDatabase();
        Cursor cursor = db.rawQuery(selectQuery, null);
        
        if (cursor.moveToFirst()) {
            do {
                Service service = new Service();
                service.setServiceNo(cursor.getInt(cursor.getColumnIndexOrThrow(COLUMN_SERVICE_NO)));
                service.setServiceName(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_SERVICE_NAME)));
                service.setType(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_TYPE)));
                service.setPrice(cursor.getDouble(cursor.getColumnIndexOrThrow(COLUMN_PRICE)));
                
                serviceList.add(service);
            } while (cursor.moveToNext());
        }
        
        cursor.close();
        db.close();
        return serviceList;
    }

    // Delete a service
    public void deleteService(int serviceNo) {
        SQLiteDatabase db = this.getWritableDatabase();
        db.delete(TABLE_SERVICES, COLUMN_SERVICE_NO + " = ?", 
                new String[]{String.valueOf(serviceNo)});
        db.close();
    }

    // Update a service
    public int updateService(Service service) {
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();
        
        values.put(COLUMN_SERVICE_NAME, service.getServiceName());
        values.put(COLUMN_TYPE, service.getType());
        values.put(COLUMN_PRICE, service.getPrice());
        
        int rowsAffected = db.update(TABLE_SERVICES, values, 
                COLUMN_SERVICE_NO + " = ?", 
                new String[]{String.valueOf(service.getServiceNo())});
        db.close();
        return rowsAffected;
    }

    // Get service by ID
    public Service getService(int serviceNo) {
        SQLiteDatabase db = this.getReadableDatabase();
        Cursor cursor = db.query(TABLE_SERVICES, 
                new String[]{COLUMN_SERVICE_NO, COLUMN_SERVICE_NAME, COLUMN_TYPE, COLUMN_PRICE},
                COLUMN_SERVICE_NO + "=?", 
                new String[]{String.valueOf(serviceNo)}, 
                null, null, null, null);
        
        if (cursor != null) {
            cursor.moveToFirst();
            Service service = new Service(
                    cursor.getInt(cursor.getColumnIndexOrThrow(COLUMN_SERVICE_NO)),
                    cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_SERVICE_NAME)),
                    cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_TYPE)),
                    cursor.getDouble(cursor.getColumnIndexOrThrow(COLUMN_PRICE))
            );
            cursor.close();
            db.close();
            return service;
        }
        db.close();
        return null;
    }
}
