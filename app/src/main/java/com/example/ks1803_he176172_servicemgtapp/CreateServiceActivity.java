package com.example.ks1803_he176172_servicemgtapp;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

public class CreateServiceActivity extends AppCompatActivity {

    private EditText etServiceNo, etServiceName, etPrice;
    private Spinner spinnerType;
    private Button btnSave, btnBack;
    private DatabaseHelper databaseHelper;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_create_service);

        // Initialize views
        initViews();
        
        // Initialize database helper
        databaseHelper = new DatabaseHelper(this);
        
        // Setup spinner
        setupSpinner();
        
        // Set click listeners
        setClickListeners();
    }

    private void initViews() {
        etServiceNo = findViewById(R.id.etServiceNo);
        etServiceName = findViewById(R.id.etServiceName);
        etPrice = findViewById(R.id.etPrice);
        spinnerType = findViewById(R.id.spinnerType);
        btnSave = findViewById(R.id.btnSave);
        btnBack = findViewById(R.id.btnBack);
    }

    private void setupSpinner() {
        // Create array of service types
        String[] serviceTypes = {
            "Housing", 
            "Health care", 
            "Baby care",
            "Education",
            "Transportation",
            "Food & Beverage",
            "Entertainment",
            "Technology"
        };
        
        // Create adapter for spinner
        ArrayAdapter<String> adapter = new ArrayAdapter<>(
            this, 
            android.R.layout.simple_spinner_item, 
            serviceTypes
        );
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerType.setAdapter(adapter);
    }

    private void setClickListeners() {
        btnBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish(); // Go back to previous activity
            }
        });

        btnSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                saveService();
            }
        });
    }

    private void saveService() {
        // Get input values
        String serviceName = etServiceName.getText().toString().trim();
        String type = spinnerType.getSelectedItem().toString();
        String priceStr = etPrice.getText().toString().trim();

        // Validate inputs
        if (serviceName.isEmpty()) {
            etServiceName.setError("Service name is required");
            etServiceName.requestFocus();
            return;
        }

        if (priceStr.isEmpty()) {
            etPrice.setError("Price is required");
            etPrice.requestFocus();
            return;
        }

        double price;
        try {
            price = Double.parseDouble(priceStr);
            if (price < 0) {
                etPrice.setError("Price must be positive");
                etPrice.requestFocus();
                return;
            }
        } catch (NumberFormatException e) {
            etPrice.setError("Invalid price format");
            etPrice.requestFocus();
            return;
        }

        // Create new service object
        Service service = new Service(serviceName, type, price);

        // Save to database
        long result = databaseHelper.addService(service);

        if (result != -1) {
            Toast.makeText(this, "Service saved successfully!", Toast.LENGTH_SHORT).show();
            
            // Clear form
            clearForm();
            
            // Go back to service list
            Intent intent = new Intent(CreateServiceActivity.this, ServiceListActivity.class);
            startActivity(intent);
            finish();
        } else {
            Toast.makeText(this, "Failed to save service", Toast.LENGTH_SHORT).show();
        }
    }

    private void clearForm() {
        etServiceName.setText("");
        etPrice.setText("");
        spinnerType.setSelection(0);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (databaseHelper != null) {
            databaseHelper.close();
        }
    }
}
