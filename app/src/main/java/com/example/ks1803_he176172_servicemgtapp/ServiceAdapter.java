package com.example.ks1803_he176172_servicemgtapp;

import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

public class ServiceAdapter extends RecyclerView.Adapter<ServiceAdapter.ServiceViewHolder> {

    private Context context;
    private List<Service> serviceList;
    private DatabaseHelper databaseHelper;
    private OnServiceDeletedListener onServiceDeletedListener;

    public interface OnServiceDeletedListener {
        void onServiceDeleted();
    }

    public ServiceAdapter(Context context, List<Service> serviceList) {
        this.context = context;
        this.serviceList = serviceList;
        this.databaseHelper = new DatabaseHelper(context);
    }

    public void setOnServiceDeletedListener(OnServiceDeletedListener listener) {
        this.onServiceDeletedListener = listener;
    }

    @NonNull
    @Override
    public ServiceViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_service, parent, false);
        return new ServiceViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ServiceViewHolder holder, int position) {
        Service service = serviceList.get(position);
        
        // Set data to views
        holder.tvIndex.setText(String.valueOf(position + 1));
        holder.tvServiceNo.setText(generateServiceCode(service.getServiceNo()));
        holder.tvServiceName.setText(service.getServiceName());
        holder.tvType.setText(service.getType());
        holder.tvPrice.setText(String.valueOf((int)service.getPrice()));

        // Set delete button click listener
        holder.btnDelete.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showDeleteConfirmationDialog(service, position);
            }
        });
    }

    @Override
    public int getItemCount() {
        return serviceList.size();
    }

    private String generateServiceCode(int serviceNo) {
        // Generate service code like ABS-23-341, SCC-23-341, BBW-23-341
        String[] prefixes = {"ABS", "SCC", "BBW", "EDU", "TRA", "FOO", "ENT", "TEC"};
        String prefix = prefixes[serviceNo % prefixes.length];
        return prefix + "-23-" + serviceNo;
    }

    private void showDeleteConfirmationDialog(Service service, int position) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        builder.setTitle("Confirmation");
        builder.setMessage("Are you sure you want to delete?");
        
        builder.setPositiveButton("Yes", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                deleteService(service, position);
            }
        });
        
        builder.setNegativeButton("No", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        
        AlertDialog dialog = builder.create();
        dialog.show();
    }

    private void deleteService(Service service, int position) {
        try {
            // Delete from database
            databaseHelper.deleteService(service.getServiceNo());
            
            // Remove from list
            serviceList.remove(position);
            
            // Notify adapter
            notifyItemRemoved(position);
            notifyItemRangeChanged(position, serviceList.size());
            
            // Show success message
            Toast.makeText(context, "Service deleted successfully", Toast.LENGTH_SHORT).show();
            
            // Notify listener
            if (onServiceDeletedListener != null) {
                onServiceDeletedListener.onServiceDeleted();
            }
            
        } catch (Exception e) {
            Toast.makeText(context, "Failed to delete service", Toast.LENGTH_SHORT).show();
        }
    }

    public void updateServiceList(List<Service> newServiceList) {
        this.serviceList.clear();
        this.serviceList.addAll(newServiceList);
        notifyDataSetChanged();
    }

    public static class ServiceViewHolder extends RecyclerView.ViewHolder {
        TextView tvIndex, tvServiceNo, tvServiceName, tvType, tvPrice;
        Button btnDelete;

        public ServiceViewHolder(@NonNull View itemView) {
            super(itemView);
            tvIndex = itemView.findViewById(R.id.tvIndex);
            tvServiceNo = itemView.findViewById(R.id.tvServiceNo);
            tvServiceName = itemView.findViewById(R.id.tvServiceName);
            tvType = itemView.findViewById(R.id.tvType);
            tvPrice = itemView.findViewById(R.id.tvPrice);
            btnDelete = itemView.findViewById(R.id.btnDelete);
        }
    }
}
