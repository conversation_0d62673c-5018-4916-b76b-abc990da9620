package com.example.ks1803_he176172_servicemgtapp;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

public class ServiceListActivity extends AppCompatActivity implements ServiceAdapter.OnServiceDeletedListener {

    private RecyclerView recyclerViewServices;
    private Button btnCreateNewService;
    private ServiceAdapter serviceAdapter;
    private DatabaseHelper databaseHelper;
    private List<Service> serviceList;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_service_list);

        // Initialize views
        initViews();
        
        // Initialize database helper
        databaseHelper = new DatabaseHelper(this);
        
        // Setup RecyclerView
        setupRecyclerView();
        
        // Load services
        loadServices();
        
        // Set click listeners
        setClickListeners();
    }

    private void initViews() {
        recyclerViewServices = findViewById(R.id.recyclerViewServices);
        btnCreateNewService = findViewById(R.id.btnCreateNewService);
    }

    private void setupRecyclerView() {
        recyclerViewServices.setLayoutManager(new LinearLayoutManager(this));
        recyclerViewServices.setHasFixedSize(true);
    }

    private void loadServices() {
        serviceList = databaseHelper.getAllServices();
        
        if (serviceAdapter == null) {
            serviceAdapter = new ServiceAdapter(this, serviceList);
            serviceAdapter.setOnServiceDeletedListener(this);
            recyclerViewServices.setAdapter(serviceAdapter);
        } else {
            serviceAdapter.updateServiceList(serviceList);
        }
    }

    private void setClickListeners() {
        btnCreateNewService.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(ServiceListActivity.this, CreateServiceActivity.class);
                startActivity(intent);
            }
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        // Reload services when returning to this activity
        loadServices();
    }

    @Override
    public void onServiceDeleted() {
        // Reload the service list after a service is deleted
        loadServices();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (databaseHelper != null) {
            databaseHelper.close();
        }
    }
}
