<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="#f5f5f5">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="20dp">

        <Button
            android:id="@+id/btnBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="← Back to Service List"
            android:textSize="12sp"
            android:background="@android:color/transparent"
            android:textColor="#007bff"
            android:padding="8dp" />

    </LinearLayout>

    <!-- Create New Service Form -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="#ffffff"
        android:padding="20dp"
        android:layout_marginBottom="20dp"
        android:elevation="2dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Create New Service"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="20dp" />

        <!-- Service No -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="10dp">

            <TextView
                android:layout_width="100dp"
                android:layout_height="wrap_content"
                android:text="Service No"
                android:textSize="14sp" />

            <EditText
                android:id="@+id/etServiceNo"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:background="@android:drawable/edit_text"
                android:padding="8dp"
                android:enabled="false"
                android:hint="Auto generated"
                android:textSize="14sp" />

        </LinearLayout>

        <!-- Service Name -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="10dp">

            <TextView
                android:layout_width="100dp"
                android:layout_height="wrap_content"
                android:text="Service Name"
                android:textSize="14sp" />

            <EditText
                android:id="@+id/etServiceName"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:background="@android:drawable/edit_text"
                android:padding="8dp"
                android:textSize="14sp" />

        </LinearLayout>

        <!-- Type -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="10dp">

            <TextView
                android:layout_width="100dp"
                android:layout_height="wrap_content"
                android:text="Type"
                android:textSize="14sp" />

            <Spinner
                android:id="@+id/spinnerType"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:background="@android:drawable/btn_dropdown" />

        </LinearLayout>

        <!-- Price -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="20dp">

            <TextView
                android:layout_width="100dp"
                android:layout_height="wrap_content"
                android:text="Price ($)"
                android:textSize="14sp" />

            <EditText
                android:id="@+id/etPrice"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:background="@android:drawable/edit_text"
                android:padding="8dp"
                android:inputType="numberDecimal"
                android:textSize="14sp" />

        </LinearLayout>

        <!-- Save Button -->
        <Button
            android:id="@+id/btnSave"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Save"
            android:background="#28a745"
            android:textColor="#ffffff"
            android:padding="12dp"
            android:layout_gravity="center" />

    </LinearLayout>

    <!-- Screen Description -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="#ffffff"
        android:padding="15dp"
        android:elevation="2dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Screen Description"
            android:textSize="14sp"
            android:textStyle="bold"
            android:layout_marginBottom="10dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:background="#e9ecef"
            android:padding="5dp">

            <TextView
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:text="#"
                android:textStyle="bold"
                android:textSize="12sp"
                android:gravity="center" />

            <TextView
                android:layout_width="0dp"
                android:layout_weight="2"
                android:layout_height="wrap_content"
                android:text="Field Name"
                android:textStyle="bold"
                android:textSize="12sp"
                android:gravity="center" />

            <TextView
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:text="Len"
                android:textStyle="bold"
                android:textSize="12sp"
                android:gravity="center" />

            <TextView
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:text="Manda"
                android:textStyle="bold"
                android:textSize="12sp"
                android:gravity="center" />

            <TextView
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:text="Type"
                android:textStyle="bold"
                android:textSize="12sp"
                android:gravity="center" />

            <TextView
                android:layout_width="0dp"
                android:layout_weight="2"
                android:layout_height="wrap_content"
                android:text="Desc"
                android:textStyle="bold"
                android:textSize="12sp"
                android:gravity="center" />

        </LinearLayout>

        <!-- Field descriptions -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="5dp">

            <TextView
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:text="1"
                android:textSize="12sp"
                android:gravity="center" />

            <TextView
                android:layout_width="0dp"
                android:layout_weight="2"
                android:layout_height="wrap_content"
                android:text="Service No"
                android:textSize="12sp"
                android:gravity="center" />

            <TextView
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:text="10"
                android:textSize="12sp"
                android:gravity="center" />

            <TextView
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:text="Yes"
                android:textSize="12sp"
                android:gravity="center" />

            <TextView
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:text="Text"
                android:textSize="12sp"
                android:gravity="center" />

            <TextView
                android:layout_width="0dp"
                android:layout_weight="2"
                android:layout_height="wrap_content"
                android:text="Mã dịch vụ"
                android:textSize="12sp"
                android:gravity="center" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
