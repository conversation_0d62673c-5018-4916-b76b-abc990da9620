<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="8dp"
    android:background="?android:attr/selectableItemBackground">

    <TextView
        android:id="@+id/tvIndex"
        android:layout_width="0dp"
        android:layout_weight="0.5"
        android:layout_height="wrap_content"
        android:text="1"
        android:textSize="12sp"
        android:gravity="center"
        android:padding="4dp" />

    <TextView
        android:id="@+id/tvServiceNo"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:layout_height="wrap_content"
        android:text="ABS-23-341"
        android:textSize="12sp"
        android:gravity="center"
        android:padding="4dp" />

    <TextView
        android:id="@+id/tvServiceName"
        android:layout_width="0dp"
        android:layout_weight="2"
        android:layout_height="wrap_content"
        android:text="House clean"
        android:textSize="12sp"
        android:gravity="center"
        android:padding="4dp" />

    <TextView
        android:id="@+id/tvType"
        android:layout_width="0dp"
        android:layout_weight="1.5"
        android:layout_height="wrap_content"
        android:text="Housing"
        android:textSize="12sp"
        android:gravity="center"
        android:padding="4dp" />

    <TextView
        android:id="@+id/tvPrice"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:layout_height="wrap_content"
        android:text="123"
        android:textSize="12sp"
        android:gravity="center"
        android:padding="4dp" />

    <Button
        android:id="@+id/btnDelete"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:layout_height="wrap_content"
        android:text="Delete"
        android:textSize="10sp"
        android:background="#dc3545"
        android:textColor="#ffffff"
        android:padding="4dp"
        android:layout_margin="2dp" />

</LinearLayout>
